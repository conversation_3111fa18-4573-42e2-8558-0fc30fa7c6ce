"""
Competitive Swarm Optimizer (CSO) for optimizing implicit acquisition functions
"""

import numpy as np
import warnings
warnings.filterwarnings('ignore')


class CompetitiveSwarmOptimizer:
    """
    Competitive Swarm Optimizer for implicit acquisition function optimization
    """

    def __init__(self, pop_size=100, max_iters=100, phi=0.1, random_state=None):
        """
        Initialize CSO

        Args:
            pop_size: Population size (must be even)
            max_iters: Maximum number of iterations
            phi: Learning parameter
            random_state: Random seed
        """
        self.pop_size = pop_size if pop_size % 2 == 0 else pop_size + 1
        self.max_iters = max_iters
        self.phi = phi
        self.random_state = random_state

        # Population and velocities
        self.population = None
        self.velocities = None
        self.bounds = None

    def optimize(self, classifier, bounds, initial_population=None):
        """
        Optimize using the classifier-based implicit acquisition function

        Args:
            classifier: Trained neural classifier for pairwise comparisons
            bounds: Problem bounds (lower, upper)
            initial_population: Optional initial population

        Returns:
            best_solution: Best solution found
            best_value: Best acquisition function value (estimated)
        """
        if self.random_state is not None:
            np.random.seed(self.random_state)

        self.bounds = bounds
        lower_bounds, upper_bounds = bounds
        n_dims = len(lower_bounds)

        # Initialize population
        if initial_population is not None:
            self.population = initial_population.copy()
            if len(self.population) < self.pop_size:
                # Add random individuals to reach desired population size
                n_additional = self.pop_size - len(self.population)
                additional_pop = np.random.uniform(
                    lower_bounds, upper_bounds, (n_additional, n_dims)
                )
                self.population = np.vstack([self.population, additional_pop])
            elif len(self.population) > self.pop_size:
                # Randomly select individuals to match population size
                indices = np.random.choice(len(self.population), self.pop_size, replace=False)
                self.population = self.population[indices]
        else:
            self.population = np.random.uniform(
                lower_bounds, upper_bounds, (self.pop_size, n_dims)
            )

        # Ensure even population size
        if len(self.population) % 2 != 0:
            # Add one more random individual
            additional = np.random.uniform(lower_bounds, upper_bounds, (1, n_dims))
            self.population = np.vstack([self.population, additional])

        # Initialize velocities
        self.velocities = np.zeros_like(self.population)

        # Main optimization loop
        for iteration in range(self.max_iters):
            self._update_population(classifier)

        # Find best solution
        best_idx = self._find_best_solution(classifier)
        best_solution = self.population[best_idx].copy()

        # Estimate best value (this is approximate since we don't have explicit AF)
        best_value = 0.0  # Placeholder

        return best_solution, best_value
    
    def _update_population(self, classifier):
        """
        Update population using competitive learning
        
        Args:
            classifier: Trained neural classifier
        """
        # Randomly pair individuals
        indices = np.random.permutation(self.pop_size)
        losers = indices[:self.pop_size//2]
        winners = indices[self.pop_size//2:]
        
        # Perform pairwise comparisons using classifier
        comparisons = self._pairwise_comparisons(classifier, losers, winners)
        
        # Update losers and winners based on comparisons
        # If comparison result is -1, swap loser and winner
        swap_mask = comparisons == -1
        temp_losers = losers[swap_mask].copy()
        losers[swap_mask] = winners[swap_mask]
        winners[swap_mask] = temp_losers
        
        # Update velocities and positions of losers
        self._update_losers(losers, winners)
        
        # Apply bounds
        self._apply_bounds()
    
    def _pairwise_comparisons(self, classifier, losers, winners):
        """
        Perform pairwise comparisons using the classifier
        
        Args:
            classifier: Trained neural classifier
            losers: Indices of loser individuals
            winners: Indices of winner individuals
        
        Returns:
            Comparison results (-1, 0, 1)
        """
        n_pairs = len(losers)
        
        # Normalize population for classifier input
        pop_norm = (self.population - self.bounds[0]) / (self.bounds[1] - self.bounds[0])
        
        # Create pairwise input for classifier
        X_pairs = np.zeros((n_pairs, 2 * self.population.shape[1]))
        for i in range(n_pairs):
            X_pairs[i] = np.concatenate([pop_norm[losers[i]], pop_norm[winners[i]]])
        
        # Get predictions from classifier
        predictions = classifier.predict(X_pairs)
        
        return predictions
    
    def _update_losers(self, losers, winners):
        """
        Update positions and velocities of loser individuals
        
        Args:
            losers: Indices of loser individuals
            winners: Indices of winner individuals
        """
        n_dims = self.population.shape[1]
        
        # Generate random coefficients
        R1 = np.random.random((len(losers), n_dims))
        R2 = np.random.random((len(losers), n_dims))
        R3 = np.random.random((len(losers), n_dims))
        
        # Compute population mean
        pop_mean = np.mean(self.population, axis=0)
        
        # Update velocities
        loser_positions = self.population[losers]
        winner_positions = self.population[winners]
        loser_velocities = self.velocities[losers]
        
        new_velocities = (R1 * loser_velocities + 
                         self.phi * R2 * (winner_positions - loser_positions) + 
                         R3 * (1 - 0) * (pop_mean - loser_positions))
        
        # Update positions
        new_positions = loser_positions + new_velocities
        
        # Store updates
        self.velocities[losers] = new_velocities
        self.population[losers] = new_positions
    
    def _apply_bounds(self):
        """
        Apply bounds to population
        """
        lower_bounds, upper_bounds = self.bounds
        
        # Clip positions to bounds
        self.population = np.clip(self.population, lower_bounds, upper_bounds)
    
    def _find_best_solution(self, classifier):
        """
        Find the best solution in the current population
        
        Args:
            classifier: Trained neural classifier
        
        Returns:
            Index of best solution
        """
        # Since we don't have explicit fitness values, we'll use a tournament approach
        # Compare each individual with a few others and count wins
        
        n_tournaments = min(10, self.pop_size // 2)
        win_counts = np.zeros(self.pop_size)
        
        pop_norm = (self.population - self.bounds[0]) / (self.bounds[1] - self.bounds[0])
        
        for _ in range(n_tournaments):
            # Random pairings
            indices = np.random.permutation(self.pop_size)
            pairs1 = indices[:self.pop_size//2]
            pairs2 = indices[self.pop_size//2:]
            
            # Create pairwise input
            X_pairs = np.zeros((len(pairs1), 2 * self.population.shape[1]))
            for i in range(len(pairs1)):
                X_pairs[i] = np.concatenate([pop_norm[pairs1[i]], pop_norm[pairs2[i]]])
            
            # Get predictions
            predictions = classifier.predict(X_pairs)
            
            # Count wins
            win_counts[pairs1[predictions == 1]] += 1
            win_counts[pairs2[predictions == -1]] += 1
        
        # Return index of individual with most wins
        return np.argmax(win_counts)
    
    def get_population(self):
        """
        Get current population
        
        Returns:
            Current population
        """
        return self.population.copy() if self.population is not None else None
    
    def set_population(self, population):
        """
        Set population
        
        Args:
            population: New population
        """
        self.population = population.copy()
        if self.velocities is None or self.velocities.shape != population.shape:
            self.velocities = np.zeros_like(population)


def optimize_implicit_acquisition_function(classifier, bounds, pop_size=100, max_iters=100,
                                         initial_population=None, random_state=None):
    """
    Optimize implicit acquisition function using CSO
    
    Args:
        classifier: Trained neural classifier
        bounds: Problem bounds (lower, upper)
        pop_size: Population size
        max_iters: Maximum iterations
        initial_population: Optional initial population
        random_state: Random seed
    
    Returns:
        best_solution: Best solution found
        optimizer: CSO optimizer instance
    """
    optimizer = CompetitiveSwarmOptimizer(
        pop_size=pop_size,
        max_iters=max_iters,
        random_state=random_state
    )
    
    best_solution, _ = optimizer.optimize(classifier, bounds, initial_population)
    
    return best_solution, optimizer
