#!/usr/bin/env python3
"""
基准测试脚本：18客户端，10维，110次评估（50初始+60迭代），Non-IID分区，1轮
"""

import numpy as np
import pandas as pd
import time
import os
from pyiaf import IAF_FBO
from pyiaf.Tasks.benchmark import create_tasks_diff_func

def run_benchmark_test():
    """运行18客户端基准测试"""
    
    # 测试参数
    n_clients = 18
    dimension = 10
    n_initial = 50
    max_iterations = 60
    total_evaluations = n_initial + max_iterations  # 110次评估
    
    print(f"开始基准测试:")
    print(f"- 客户端数量: {n_clients}")
    print(f"- 问题维度: {dimension}")
    print(f"- 初始采样: {n_initial}")
    print(f"- 最大迭代: {max_iterations}")
    print(f"- 总评估次数: {total_evaluations}")
    print(f"- 分区策略: Non-IID (np_per_dim=2)")
    print("-" * 50)
    
    # 创建18个不同的任务函数
    tasks = create_tasks_diff_func(dim=dimension, normalized=False)
    print(f"创建了 {len(tasks)} 个基准任务")
    
    # 使用函数特定的边界（与MATLAB版本一致）
    client_bounds = []
    for task in tasks:
        if hasattr(task, 'x_lb') and hasattr(task, 'x_ub'):
            lower = np.full(dimension, task.x_lb)
            upper = np.full(dimension, task.x_ub)
            client_bounds.append((lower, upper))
        else:
            # 默认边界
            lower = np.full(dimension, -5.0)
            upper = np.full(dimension, 5.0)
            client_bounds.append((lower, upper))

    print(f"函数特定边界设置完成")

    # 打印每个客户端的边界信息
    print("客户端边界信息:")
    for i, (task, bounds) in enumerate(zip(tasks, client_bounds)):
        task_name = type(task).__name__
        print(f"  客户端{i:2d}: {task_name:12s} 边界=[{bounds[0][0]:6.1f}, {bounds[1][0]:6.1f}]")
    print()
    
    # 创建IAF-FBO优化器 (与MATLAB参数完全对齐)
    optimizer = IAF_FBO(
        n_clients=n_clients,
        bounds=client_bounds,  # 使用Non-IID分区边界
        n_initial=n_initial,    # N = 50 (MATLAB)
        max_iterations=max_iterations,  # MAXFE = 60 (MATLAB)
        af_type='LCB',         # UCB_Flag = 2 (MATLAB)
        n_clusters=6,          # cl_num = 6 (MATLAB)
        pop_size=100,          # popsize = 100 (MATLAB)
        cso_iters=100,         # wmax = 100 (MATLAB)
        transfer_prob=0.5,     # rand < 0.5 (MATLAB)
        noise_prob=0.0,        # p = 0 (MATLAB)
        random_state=42
    )
    
    print(f"IAF-FBO优化器创建完成")
    
    # 设置目标函数
    objective_functions = {}
    for client_id in range(n_clients):
        task = tasks[client_id]
        objective_functions[client_id] = lambda x, t=task: t(x)
    
    print(f"目标函数设置完成")
    
    # 运行优化
    print("开始优化...")
    start_time = time.time()
    
    optimizer.setup_clients(objective_functions)
    results = optimizer.run_optimization()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"优化完成，总耗时: {total_time:.2f}秒")
    print("-" * 50)
    
    # 输出结果
    print("优化结果:")
    best_values = []
    for client_id in range(n_clients):
        best_val = results['best_values'][client_id]
        best_values.append(best_val)
        print(f"客户端 {client_id:2d}: 最优值 = {best_val:10.6f}")
    
    print("-" * 50)
    print(f"统计信息:")
    print(f"- 平均最优值: {np.mean(best_values):.6f}")
    print(f"- 最优值标准差: {np.std(best_values):.6f}")
    print(f"- 最好结果: {np.min(best_values):.6f}")
    print(f"- 最差结果: {np.max(best_values):.6f}")
    
    # 保存每个客户端的110次真实评估值（转置格式：行为评估轮次，列为客户端）
    client_results = []
    for client_id in range(n_clients):
        eval_history = optimizer.history['evaluation_history'][client_id]
        # 确保每个客户端有110次评估
        if len(eval_history) >= 110:
            client_110_values = eval_history[:110]
        else:
            # 如果不足110次，用最后的值填充
            client_110_values = eval_history + [eval_history[-1]] * (110 - len(eval_history))

        client_results.append(client_110_values)

    # 转换为DataFrame并转置：行为评估轮次，列为客户端
    client_df = pd.DataFrame(client_results).T  # 转置
    client_df.columns = [f'Client_{i}' for i in range(n_clients)]  # 设置列名为客户端
    client_df.index.name = 'Evaluation_Round'  # 设置行索引名为评估轮次

    # 保存客户端评估结果
    client_filename = f"client_evaluations_18clients_10d_110evals_niid2.csv"
    client_df.to_csv(client_filename)
    print(f"客户端评估历史已保存到: {client_filename} (行：评估轮次，列：客户端)")
    
    return results, total_time

if __name__ == "__main__":
    try:
        results, runtime = run_benchmark_test()
        print(f"\n测试成功完成！总运行时间: {runtime:.2f}秒")
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
