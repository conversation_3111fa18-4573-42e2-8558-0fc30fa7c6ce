#!/usr/bin/env python3
"""
比较两个msgpack文件的结构差异
"""

import msgpack
import numpy as np
from pathlib import Path
import json


def _decode_hook(obj: dict):
    """msgpack解码钩子函数"""
    if "__ndarray__" in obj:
        return np.frombuffer(obj["data"], dtype=obj["dtype"]).reshape(obj["shape"])
    elif "__set__" in obj:
        return set(obj["items"])
    return obj


def load_msgpack(filename: Path) -> dict:
    """加载msgpack格式的数据"""
    with open(filename, 'rb') as f:
        # 尝试不同的解包方式
        try:
            data = msgpack.unpackb(f.read(), object_hook=_decode_hook, raw=False)
        except Exception as e1:
            print(f"  尝试raw=False失败: {e1}")
            f.seek(0)
            try:
                data = msgpack.unpackb(f.read(), object_hook=_decode_hook, raw=True, strict_map_key=False)
            except Exception as e2:
                print(f"  尝试raw=True失败: {e2}")
                f.seek(0)
                # 最后尝试不使用object_hook
                data = msgpack.unpackb(f.read(), raw=False, strict_map_key=False)
    return data


def analyze_structure(data, prefix="", max_depth=3, current_depth=0):
    """递归分析数据结构"""
    if current_depth > max_depth:
        return ["... (深度限制)"]
    
    result = []
    
    if isinstance(data, dict):
        result.append(f"{prefix}dict (keys: {len(data)})")
        for key, value in list(data.items())[:5]:  # 只显示前5个键
            key_str = str(key)[:50]  # 限制键名长度
            if isinstance(value, np.ndarray):
                result.append(f"{prefix}  {key_str}: ndarray {value.shape} {value.dtype}")
            elif isinstance(value, (list, tuple)):
                result.append(f"{prefix}  {key_str}: {type(value).__name__} (len: {len(value)})")
                if len(value) > 0:
                    result.extend(analyze_structure(value[0], f"{prefix}    [0]: ", max_depth, current_depth+1))
            elif isinstance(value, dict):
                result.append(f"{prefix}  {key_str}: dict (keys: {len(value)})")
                result.extend(analyze_structure(value, f"{prefix}    ", max_depth, current_depth+1))
            else:
                value_str = str(value)[:100]  # 限制值的显示长度
                result.append(f"{prefix}  {key_str}: {type(value).__name__} = {value_str}")
        
        if len(data) > 5:
            result.append(f"{prefix}  ... (还有 {len(data)-5} 个键)")
            
    elif isinstance(data, (list, tuple)):
        result.append(f"{prefix}{type(data).__name__} (len: {len(data)})")
        if len(data) > 0:
            result.extend(analyze_structure(data[0], f"{prefix}  [0]: ", max_depth, current_depth+1))
        if len(data) > 1:
            result.append(f"{prefix}  ... (还有 {len(data)-1} 个元素)")
    
    elif isinstance(data, np.ndarray):
        result.append(f"{prefix}ndarray {data.shape} {data.dtype}")
    
    else:
        value_str = str(data)[:100]
        result.append(f"{prefix}{type(data).__name__} = {value_str}")
    
    return result


def compare_msgpack_files(file1: Path, file2: Path):
    """比较两个msgpack文件"""
    print(f"比较文件:")
    print(f"  文件1: {file1}")
    print(f"  文件2: {file2}")
    print("="*80)
    
    try:
        data1 = load_msgpack(file1)
        print(f"\n📁 文件1结构:")
        structure1 = analyze_structure(data1)
        for line in structure1:
            print(line)
            
    except Exception as e:
        print(f"❌ 读取文件1失败: {e}")
        return
    
    try:
        data2 = load_msgpack(file2)
        print(f"\n📁 文件2结构:")
        structure2 = analyze_structure(data2)
        for line in structure2:
            print(line)
            
    except Exception as e:
        print(f"❌ 读取文件2失败: {e}")
        return
    
    # 比较基本信息
    print(f"\n🔍 基本比较:")
    
    # 检查顶层键
    if isinstance(data1, dict) and isinstance(data2, dict):
        keys1 = set(data1.keys())
        keys2 = set(data2.keys())
        
        print(f"  文件1顶层键: {sorted(keys1)}")
        print(f"  文件2顶层键: {sorted(keys2)}")
        
        common_keys = keys1 & keys2
        only_in_1 = keys1 - keys2
        only_in_2 = keys2 - keys1
        
        if common_keys:
            print(f"  共同键: {sorted(common_keys)}")
        if only_in_1:
            print(f"  仅在文件1: {sorted(only_in_1)}")
        if only_in_2:
            print(f"  仅在文件2: {sorted(only_in_2)}")
        
        # 如果都有_solutions键，比较客户端数量
        if '_solutions' in data1 and '_solutions' in data2:
            solutions1 = data1['_solutions']
            solutions2 = data2['_solutions']
            
            if isinstance(solutions1, dict) and isinstance(solutions2, dict):
                print(f"  文件1客户端数量: {len(solutions1)}")
                print(f"  文件2客户端数量: {len(solutions2)}")
                
                # 比较第一个客户端的结构
                if solutions1 and solutions2:
                    client1_key = list(solutions1.keys())[0]
                    client2_key = list(solutions2.keys())[0]
                    
                    client1_data = solutions1[client1_key]
                    client2_data = solutions2[client2_key]
                    
                    if isinstance(client1_data, dict) and isinstance(client2_data, dict):
                        print(f"\n  第一个客户端字段比较:")
                        fields1 = set(client1_data.keys())
                        fields2 = set(client2_data.keys())
                        
                        print(f"    文件1字段: {sorted(fields1)}")
                        print(f"    文件2字段: {sorted(fields2)}")
                        
                        common_fields = fields1 & fields2
                        if common_fields:
                            print(f"    共同字段: {sorted(common_fields)}")
                            
                            # 比较一些关键字段的值
                            for field in ['_dim', '_obj', '_fe_init', 'algorithm']:
                                if field in client1_data and field in client2_data:
                                    val1 = client1_data[field]
                                    val2 = client2_data[field]
                                    print(f"    {field}: {val1} vs {val2}")


def main():
    """主函数"""
    file1 = Path("Run 1.msgpack")  # 原有文件
    file2 = Path("10D_init50_110FE_msgpack/Run 1.msgpack")  # 新转换的文件
    
    if not file1.exists():
        print(f"❌ 文件1不存在: {file1}")
        return
        
    if not file2.exists():
        print(f"❌ 文件2不存在: {file2}")
        return
    
    compare_msgpack_files(file1, file2)


if __name__ == "__main__":
    main()
