"""
Federated Learning Framework for IAF-FBO algorithm
"""

import numpy as np

# 修复相对导入问题
try:
    from .utils import kmeans_clustering
    from .neural_classifier import aggregate_classifiers
except ImportError:
    from utils import kmeans_clustering
    from neural_classifier import aggregate_classifiers
import warnings
warnings.filterwarnings('ignore')


class FederatedClient:
    """
    Federated client for IAF-FBO
    """
    
    def __init__(self, client_id, bounds, initial_data=None, gp_model=None, 
                 classifier=None, random_state=None):
        """
        Initialize federated client
        
        Args:
            client_id: Unique client identifier
            bounds: Problem bounds (lower, upper)
            initial_data: Initial training data (X, y)
            gp_model: Gaussian Process model
            classifier: Neural classifier
            random_state: Random seed
        """
        self.client_id = client_id
        self.bounds = bounds
        self.random_state = random_state
        
        # Data
        if initial_data is not None:
            self.X_data, self.y_data = initial_data
        else:
            self.X_data = np.empty((0, len(bounds[0])))
            self.y_data = np.empty(0)
        
        # Models
        self.gp_model = gp_model
        self.classifier = classifier
        
        # History
        self.new_X = []
        self.new_y = []
        self.fail_count = 0
        
    def update_data(self, X_new, y_new):
        """
        Update client data with new observations

        Args:
            X_new: New input data
            y_new: New target values
        """
        X_new = np.asarray(X_new)
        y_new = np.asarray(y_new)

        if X_new.ndim == 1:
            X_new = X_new.reshape(1, -1)
        if y_new.ndim == 0:
            y_new = np.array([y_new])

        self.X_data = np.vstack([self.X_data, X_new])
        self.y_data = np.concatenate([self.y_data, y_new])

        # Update history
        self.new_X.append(X_new)
        self.new_y.append(y_new)

        # Update fail count
        if len(self.new_y) >= 2:
            if np.min(self.new_y[-1]) == np.min(self.new_y[-2]):
                self.fail_count += 1
            else:
                self.fail_count = 0
    
    def get_data(self):
        """
        Get client data
        
        Returns:
            X_data, y_data: Client's training data
        """
        return self.X_data.copy(), self.y_data.copy()
    
    def get_weight_vector(self, fixed_size=100):
        """
        Get classifier weight vector for similarity computation

        Args:
            fixed_size: Fixed size for weight vector to ensure consistency

        Returns:
            Weight vector of fixed size
        """
        if self.classifier is not None and self.classifier.is_fitted:
            weights = self.classifier.get_weights()
            # Ensure fixed size by truncating or padding
            if len(weights) >= fixed_size:
                return weights[:fixed_size]
            else:
                # Pad with zeros if too short
                padded = np.zeros(fixed_size)
                padded[:len(weights)] = weights
                return padded
        else:
            # Return random vector if classifier not fitted
            return np.random.randn(fixed_size)


class FederatedServer:
    """
    Federated server for IAF-FBO
    """
    
    def __init__(self, n_clusters=3, similarity_method='euclidean', random_state=None):
        """
        Initialize federated server
        
        Args:
            n_clusters: Number of clusters for client grouping
            similarity_method: Method for computing client similarity
            random_state: Random seed
        """
        self.n_clusters = n_clusters
        self.similarity_method = similarity_method
        self.random_state = random_state
        
        # Client management
        self.clients = {}
        self.client_groups = {}
        self.global_classifiers = {}
        
    def register_client(self, client):
        """
        Register a new client
        
        Args:
            client: FederatedClient instance
        """
        self.clients[client.client_id] = client
    
    def cluster_clients(self):
        """
        Cluster clients based on classifier weight similarity
        
        Returns:
            client_groups: Dictionary mapping group_id to list of client_ids
        """
        if len(self.clients) < self.n_clusters:
            # If fewer clients than clusters, each client is its own group
            self.client_groups = {i: [client_id] for i, client_id in enumerate(self.clients.keys())}
            return self.client_groups
        
        # Get weight vectors from all clients
        client_ids = list(self.clients.keys())
        weight_vectors = []
        
        for client_id in client_ids:
            weight_vector = self.clients[client_id].get_weight_vector()
            weight_vectors.append(weight_vector)
        
        weight_vectors = np.array(weight_vectors)
        
        # Perform clustering
        cluster_labels, _ = kmeans_clustering(
            weight_vectors, 
            self.n_clusters, 
            random_state=self.random_state
        )
        
        # Group clients by cluster
        self.client_groups = {}
        for i, client_id in enumerate(client_ids):
            group_id = cluster_labels[i]
            if group_id not in self.client_groups:
                self.client_groups[group_id] = []
            self.client_groups[group_id].append(client_id)
        
        return self.client_groups
    
    def aggregate_classifiers(self):
        """
        Aggregate classifiers within each client group
        
        Returns:
            global_classifiers: Dictionary mapping group_id to aggregated classifier
        """
        self.global_classifiers = {}
        
        for group_id, client_ids in self.client_groups.items():
            # Get classifiers from clients in this group
            classifiers = {}
            for client_id in client_ids:
                client = self.clients[client_id]
                if client.classifier is not None and client.classifier.is_fitted:
                    classifiers[client_id] = client.classifier
            
            if classifiers:
                # Aggregate classifiers
                agg_classifier = aggregate_classifiers(classifiers, list(classifiers.keys()))
                self.global_classifiers[group_id] = agg_classifier
        
        return self.global_classifiers
    
    def get_global_classifier(self, client_id):
        """
        Get global classifier for a specific client
        
        Args:
            client_id: Client identifier
        
        Returns:
            Global classifier for the client's group
        """
        # Find which group the client belongs to
        for group_id, client_ids in self.client_groups.items():
            if client_id in client_ids:
                return self.global_classifiers.get(group_id, None)
        
        return None
    
    def broadcast_classifiers(self):
        """
        Broadcast global classifiers to clients
        """
        for group_id, client_ids in self.client_groups.items():
            if group_id in self.global_classifiers:
                global_classifier = self.global_classifiers[group_id]
                
                for client_id in client_ids:
                    # Option 1: Replace client classifier with global one
                    # self.clients[client_id].classifier = global_classifier.copy()
                    
                    # Option 2: Keep local classifier but provide access to global one
                    # This is more flexible for the optimization phase
                    pass
    
    def get_client_group(self, client_id):
        """
        Get the group ID for a specific client

        Args:
            client_id: Client identifier

        Returns:
            Group ID
        """
        for group_id, client_ids in self.client_groups.items():
            if client_id in client_ids:
                return group_id
        return None

    def optimize_acquisition_functions(self, clients, pop_size, cso_iters, transfer_prob, random_state):
        """
        Server-side optimization of acquisition functions using CSO
        (Following MATLAB original design where server performs CSO optimization)

        Args:
            clients: Dictionary of client objects
            pop_size: CSO population size
            cso_iters: CSO iterations
            transfer_prob: Probability of using global classifier
            random_state: Random seed

        Returns:
            new_points: Dictionary mapping client_id to new query point
        """
        from .competitive_swarm_optimizer import CompetitiveSwarmOptimizer
        import numpy as np

        new_points = {}

        for client_id, client in clients.items():
            # Server decides whether to use global or local classifier (MATLAB line 313)
            use_global = np.random.random() < transfer_prob

            if use_global and hasattr(self, 'global_classifiers'):
                # Use aggregated global classifier (MATLAB line 318-320)
                group_id = self.get_client_group(client_id)
                if group_id is not None and group_id in self.global_classifiers:
                    classifier = self.global_classifiers[group_id]
                else:
                    classifier = client.classifier
            else:
                # Use local classifier (MATLAB line 323)
                classifier = client.classifier

            if classifier is None or not classifier.is_fitted:
                # Fallback to random sampling
                bounds = client.bounds
                new_point = np.random.uniform(bounds[0], bounds[1])
                new_points[client_id] = new_point
                continue

            # Prepare initial population for CSO (MATLAB line 337-375)
            initial_pop = None
            if len(client.new_X) > 0:
                # Use previous solutions as initial population (MATLAB line 340)
                initial_pop = np.array(client.new_X[-min(10, len(client.new_X)):])
                if len(initial_pop.shape) == 3:
                    initial_pop = initial_pop.reshape(-1, initial_pop.shape[-1])

            # Server performs CSO optimization (MATLAB line 383-432)
            cso = CompetitiveSwarmOptimizer(
                pop_size=pop_size,
                max_iters=cso_iters,
                phi=0.1,  # MATLAB line 289: phi = 0.1
                random_state=random_state + client_id if random_state else None
            )

            best_point, _ = cso.optimize(classifier, client.bounds, initial_pop)
            new_points[client_id] = best_point

        return new_points

    def optimize_acquisition_functions_parallel(self, clients, pop_size, cso_iters, transfer_prob, random_state, n_processes):
        """
        Server-side parallel optimization of acquisition functions using CSO
        (Following MATLAB original design with parallel acceleration)

        Args:
            clients: Dictionary of client objects
            pop_size: CSO population size
            cso_iters: CSO iterations
            transfer_prob: Probability of using global classifier
            random_state: Random seed
            n_processes: Number of parallel processes

        Returns:
            new_points: Dictionary mapping client_id to new query point
        """
        from multiprocessing import Pool
        import numpy as np

        # Prepare arguments for parallel processing
        args_list = []
        for client_id, client in clients.items():
            # Server decides whether to use global or local classifier
            use_global = np.random.random() < transfer_prob

            if use_global and hasattr(self, 'global_classifiers'):
                # Use aggregated global classifier
                group_id = self.get_client_group(client_id)
                if group_id is not None and group_id in self.global_classifiers:
                    classifier = self.global_classifiers[group_id]
                else:
                    classifier = client.classifier
            else:
                # Use local classifier
                classifier = client.classifier

            # Prepare initial population for CSO
            initial_pop = None
            if len(client.new_X) > 0:
                initial_pop = np.array(client.new_X[-min(10, len(client.new_X)):])
                if len(initial_pop.shape) == 3:
                    initial_pop = initial_pop.reshape(-1, initial_pop.shape[-1])

            args_list.append((
                client_id, classifier, client.bounds,
                pop_size, cso_iters, initial_pop, random_state
            ))

        # Parallel CSO optimization
        with Pool(processes=n_processes) as pool:
            results = pool.map(_optimize_client_acquisition_server, args_list)

        # Collect results
        new_points = {}
        for client_id, best_point in results:
            new_points[client_id] = best_point

        return new_points

    def get_similar_clients(self, client_id):
        """
        Get list of clients similar to the given client
        
        Args:
            client_id: Client identifier
        
        Returns:
            List of similar client IDs
        """
        group_id = self.get_client_group(client_id)
        if group_id is not None:
            return [cid for cid in self.client_groups[group_id] if cid != client_id]
        return []


def create_federated_setup(client_configs, n_clusters=3, random_state=None):
    """
    Create a federated setup with multiple clients and a server
    
    Args:
        client_configs: List of client configuration dictionaries
        n_clusters: Number of clusters for client grouping
        random_state: Random seed
    
    Returns:
        server: FederatedServer instance
        clients: Dictionary of FederatedClient instances
    """
    # Create server
    server = FederatedServer(n_clusters=n_clusters, random_state=random_state)
    
    # Create clients
    clients = {}
    for config in client_configs:
        client = FederatedClient(**config)
        clients[client.client_id] = client
        server.register_client(client)
    
    return server, clients


def _optimize_client_acquisition_server(args):
    """
    Server-side CSO optimization for a single client (parallel worker function)
    Following MATLAB original design where server performs CSO optimization
    """
    try:
        client_id, classifier, bounds, pop_size, cso_iters, initial_pop, random_state = args

        if classifier is None or not classifier.is_fitted:
            # Fallback to random sampling
            import numpy as np
            new_point = np.random.uniform(bounds[0], bounds[1])
            return client_id, new_point

        # Server performs CSO optimization (MATLAB line 383-432)
        try:
            from .competitive_swarm_optimizer import CompetitiveSwarmOptimizer
        except ImportError:
            from competitive_swarm_optimizer import CompetitiveSwarmOptimizer

        cso = CompetitiveSwarmOptimizer(
            pop_size=pop_size,
            max_iters=cso_iters,
            phi=0.1,  # MATLAB line 289: phi = 0.1
            random_state=random_state + client_id if random_state else None
        )

        best_point, _ = cso.optimize(classifier, bounds, initial_pop)
        return client_id, best_point

    except Exception as e:
        print(f"服务器为客户端 {client_id} 优化采集函数失败: {e}")
        import numpy as np
        new_point = np.random.uniform(bounds[0], bounds[1])
        return client_id, new_point
