"""
PyIAF: Python Implementation of Implicit Acquisition Function for Federated Bayesian Optimization

This package implements the IAF-FBO algorithm as described in:
"Optimization of an Implicit Acquisition Function for Federated Bayesian Many-Task Optimization"

Main components:
- IAF_FBO: Main algorithm class
- GaussianProcess: Gaussian process surrogate model
- NeuralClassifier: Neural network for pairwise comparisons
- CompetitiveSwarmOptimizer: CSO for optimizing implicit acquisition functions
- FederatedFramework: Client-server architecture for federated learning
"""

from .iaf_fbo import IAF_FBO
from .parallel_iaf_fbo import ParallelIAF_FBO
from .gaussian_process import GaussianProcess
from .neural_classifier import NeuralClassifier, aggregate_classifiers, train_pairwise_classifier
from .competitive_swarm_optimizer import CompetitiveSwarmOptimizer, optimize_implicit_acquisition_function
from .federated_framework import FederatedServer, FederatedClient, create_federated_setup
# Benchmark functions removed - use Tasks/benchmark.py instead
from .partitions import (
    create_normalized_partitions
)
from .utils import (
    lhs_classic, rbf_kernel, acquisition_function, create_pairwise_data,
    kmeans_clustering, one_hot_encode, one_hot_decode
)

__version__ = "1.0.0"
__author__ = "PyIAF Team"
__email__ = "<EMAIL>"

__all__ = [
    # Main algorithm
    'IAF_FBO',
    'ParallelIAF_FBO',

    # Core components
    'GaussianProcess',
    'NeuralClassifier',
    'CompetitiveSwarmOptimizer',
    'FederatedServer',
    'FederatedClient',

    # Benchmark functions - use Tasks/benchmark.py instead

    # Partitioning strategies
    'create_iid_partitions',
    'create_non_iid_partitions',
    'create_random_partitions',
    'create_clustered_partitions',
    'create_hierarchical_partitions',
    'create_partitions',
    'visualize_partitions',
    'get_partition_info',

    # Utility functions
    'lhs_classic',
    'rbf_kernel',
    'acquisition_function',
    'create_pairwise_data',
    'kmeans_clustering',
    'one_hot_encode',
    'one_hot_decode',

    # High-level functions
    'aggregate_classifiers',
    'train_pairwise_classifier',
    'optimize_implicit_acquisition_function',
    'create_federated_setup',
]


def get_version():
    """Get package version"""
    return __version__


def get_info():
    """Get package information"""
    return {
        'name': 'PyIAF',
        'version': __version__,
        'description': 'Python Implementation of Implicit Acquisition Function for Federated Bayesian Optimization',
        'author': __author__,
        'email': __email__,
    }


# Example usage and quick start guide
EXAMPLE_USAGE = """
# Quick Start Example

import numpy as np
from pyiaf import IAF_FBO

# Define objective functions for each client
def objective_1(x):
    return np.sum(x**2)  # Sphere function

def objective_2(x):
    return np.sum((x - 1)**2)  # Shifted sphere function

# Define problem bounds
bounds = (np.array([-5, -5]), np.array([5, 5]))  # 2D problem

# Create IAF-FBO instance
optimizer = IAF_FBO(
    n_clients=2,
    bounds=bounds,
    n_initial=20,
    max_iterations=30,
    af_type='LCB',
    random_state=42
)

# Setup clients with objective functions
objective_functions = {
    0: objective_1,
    1: objective_2
}
optimizer.setup_clients(objective_functions)

# Run optimization
results = optimizer.run_optimization()

# Print results
for client_id in results['best_solutions']:
    print(f"Client {client_id}:")
    print(f"  Best solution: {results['best_solutions'][client_id]}")
    print(f"  Best value: {results['best_values'][client_id]:.6f}")
"""

def print_example():
    """Print example usage"""
    print(EXAMPLE_USAGE)


# Configuration and default parameters
DEFAULT_CONFIG = {
    'n_initial': 50,
    'max_iterations': 60,
    'af_type': 'LCB',
    'n_clusters': 6,
    'pop_size': 100,
    'cso_iters': 100,
    'transfer_prob': 0.5,
    'noise_prob': 0.0,
    'gp_nugget': 1e-10,
    'gp_theta_bounds': (1e-5, 100.0),
    'nn_hidden_layers': None,  # Auto-determined
    'nn_max_iter': 1000,
    'cso_phi': 0.1,
}

def get_default_config():
    """Get default configuration parameters"""
    return DEFAULT_CONFIG.copy()


# Validation functions
def validate_bounds(bounds):
    """Validate problem bounds"""
    if isinstance(bounds, tuple) and len(bounds) == 2:
        lower, upper = bounds
        if isinstance(lower, np.ndarray) and isinstance(upper, np.ndarray):
            if len(lower) == len(upper) and np.all(lower < upper):
                return True
    return False


def validate_config(config):
    """Validate configuration parameters"""
    required_keys = ['n_clients', 'bounds']
    for key in required_keys:
        if key not in config:
            raise ValueError(f"Missing required configuration key: {key}")
    
    if not isinstance(config['n_clients'], int) or config['n_clients'] < 1:
        raise ValueError("n_clients must be a positive integer")
    
    bounds = config['bounds']
    if isinstance(bounds, list):
        for i, bound in enumerate(bounds):
            if not validate_bounds(bound):
                raise ValueError(f"Invalid bounds for client {i}")
    else:
        if not validate_bounds(bounds):
            raise ValueError("Invalid bounds format")
    
    return True
