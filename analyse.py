import os
import csv
import math # 用于 float('inf')

def find_optimal_fitness_per_file(filepath, num_clients=18):
    """
    在一个CSV文件中为每个客户端找到最优（最小）适应度值。

    Args:
        filepath (str): CSV文件的路径。
        num_clients (int): 客户端的数量。

    Returns:
        list: 包含该文件中每个客户端最优适应度值的列表，如果文件无法处理则返回None。
    """
    client_optimal_fitness_in_file = [float('inf')] * num_clients
    try:
        with open(filepath, 'r', newline='') as csvfile:
            reader = csv.reader(csvfile)
            header = next(reader) # 跳过表头
            
            # 确定客户端列的索引，假设它们从第二列开始
            # 例如, client0 在索引 1, client1 在索引 2, ..., client17 在索引 18
            # CSV列：轮数, client0, client1, ..., client17, time
            
            rows_processed_for_file = 0
            for row_index, row_data in enumerate(reader):
                if not row_data: # 跳过空行
                    continue
                
                # 我们关心的是前110轮的数据，即文件中的行索引0到109（在跳过表头后）
                # 对应原始文件的行号是 2 到 111
                if row_index < 110: # 确保我们只处理前110轮
                    try:
                        # 第一个元素是轮数索引，之后是客户端数据
                        for client_idx in range(num_clients):
                            # row_data[0] 是轮数, row_data[1] 是 client0, ...
                            fitness_value_str = row_data[client_idx + 1]
                            fitness_value = float(fitness_value_str)
                            if fitness_value < client_optimal_fitness_in_file[client_idx]:
                                client_optimal_fitness_in_file[client_idx] = fitness_value
                        rows_processed_for_file +=1
                    except (ValueError, IndexError) as e:
                        # 打印警告，但继续处理文件的其余部分和其他客户端
                        print(f"警告：处理文件 '{filepath}' 第 {row_index + 2} 行, 客户端 {client_idx} 时出错: {e}。数据: '{row_data[client_idx + 1]}'")
                        continue 
            
            if rows_processed_for_file == 0:
                print(f"警告：文件 '{filepath}' 中没有成功处理任何数据行。")
                return None
            
            # 如果某个客户端在所有轮次中都没有有效数据，其最优值仍为inf
            # 我们可以选择将其替换为NaN或特定标记，或在后续平均计算中处理
            for i in range(num_clients):
                if client_optimal_fitness_in_file[i] == float('inf'):
                    print(f"警告：文件 '{filepath}' 中的客户端 {i} 没有找到有效的适应度值。")
                    # client_optimal_fitness_in_file[i] = float('nan') # 或者保持inf，平均时处理

            return client_optimal_fitness_in_file
        
    except FileNotFoundError:
        print(f"错误：文件 '{filepath}' 未找到。")
        return None
    except StopIteration: # 文件为空或只有表头
        print(f"错误：文件 '{filepath}' 为空或只有表头。")
        return None
    except Exception as e:
        print(f"处理文件 '{filepath}' 时发生未知错误：{e}")
        return None


def process_all_files_and_average(folder_path, output_filename="average_optimal_fitness_corrected.csv"):
    """
    处理文件夹中的所有CSV文件，计算每个客户端的平均最优适应度值。
    """
    all_csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv') and 'client_evaluations' in f]
    if not all_csv_files:
        print(f"在文件夹 '{folder_path}' 中没有找到CSV文件。")
        return

    num_clients = 18
    # 用于累加每个客户端从各个文件中找到的最优适应度值
    total_optimal_fitness_sums = [0.0] * num_clients
    # 记录每个客户端有多少个文件为其贡献了有效的最优值
    files_contributing_per_client = [0] * num_clients

    print(f"找到 {len(all_csv_files)} 个CSV文件进行处理...")

    for filename in all_csv_files:
        filepath = os.path.join(folder_path, filename)
        print(f"正在处理文件: {filename}...")
        optimal_fitness_this_file = find_optimal_fitness_per_file(filepath, num_clients)

        if optimal_fitness_this_file:
            for i in range(num_clients):
                if optimal_fitness_this_file[i] != float('inf') and not math.isnan(optimal_fitness_this_file[i]):
                    total_optimal_fitness_sums[i] += optimal_fitness_this_file[i]
                    files_contributing_per_client[i] += 1
                # else:
                    # print(f"文件 {filename} 的客户端 {i} 没有有效的最优值，不计入平均。")


    average_client_fitness = [0.0] * num_clients
    for i in range(num_clients):
        if files_contributing_per_client[i] > 0:
            average_client_fitness[i] = total_optimal_fitness_sums[i] / files_contributing_per_client[i]
        else:
            average_client_fitness[i] = float('nan') # 或者 0.0，或标记为无数据
            print(f"警告：客户端 {i} 在所有文件中都没有找到有效的适应度值。")

    output_filepath = os.path.join(folder_path, output_filename)
    try:
        with open(output_filepath, 'w', newline='') as outfile:
            writer = csv.writer(outfile)
            writer.writerow(['ClientIndex', 'AverageOptimalFitness'])
            for i in range(num_clients):
                writer.writerow([f'client{i}', average_client_fitness[i] if not math.isnan(average_client_fitness[i]) else 'N/A'])
        print(f"处理完成。结果已保存到 '{output_filepath}'")
    except IOError as e:
        print(f"写入输出文件 '{output_filepath}' 时出错：{e}")

if __name__ == "__main__":
    # 使用相对路径，从当前工作目录开始
    target_folder = os.path.join('pyiaf', '10D_init50_110FE')

    if os.path.isdir(target_folder):
        process_all_files_and_average(target_folder)
    else:
        print(f"错误：找不到目标文件夹 '{target_folder}'。请确保在正确的目录中运行脚本。")
